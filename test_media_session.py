#!/usr/bin/env python3
"""
Test script for the new Windows Media Session API functionality.
"""

import sys
import os

# Add the current directory to the path so we can import spotifyh
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from spotifyh import get_current_track

def test_media_session():
    """Test the get_current_track function."""
    print("Testing Windows Media Session API...")
    print("Make sure you have some media playing (Spotify, YouTube, etc.)")
    print()
    
    try:
        current_track = get_current_track()
        if current_track:
            print(f"✅ Successfully detected currently playing media:")
            print(f"   📀 {current_track}")
        else:
            print("❌ No media currently playing or unable to detect")
    except Exception as e:
        print(f"❌ Error occurred: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_media_session()
