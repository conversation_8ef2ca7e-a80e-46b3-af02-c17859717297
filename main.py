import threading
import queue
import concurrent.futures
import os
import regex
import spotify
from colorama import Fore, init
import psutil
import time
import subprocess
import requests
from bs4 import BeautifulSoup
from yt_dlp import YoutubeDL
from difflib import SequenceMatcher
import numpy as np
import itertools
import customtkinter as tk
import datetime
# import random
import win32gui
import win32con
import win32clipboard
import win32process
import spotifyh
from spotifyh import get_list
import speech_recognition as sr
import shutil
import pyautogui
import argparse


r = sr.Recognizer()


if psutil.Process(os.getpid()).parent().name() == "cmd.exe":
    init(convert=True)

path = f"{os.getcwd()}\\"
sList = []

# Global thread management
download_queue = queue.Queue()
active_downloads = {}
download_lock = threading.Lock()
max_concurrent_downloads = 3  # Maximum number of simultaneous downloads

class DownloadManager:
    """Manages multiple concurrent downloads with thread safety."""

    def __init__(self, max_workers=3):
        self.max_workers = max_workers
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)
        self.active_downloads = {}
        self.download_counter = 0
        self.lock = threading.Lock()

    def submit_download(self, download_func, *args, **kwargs):
        """Submit a download task to the thread pool."""
        with self.lock:
            self.download_counter += 1
            download_id = f"download_{self.download_counter}"

        future = self.executor.submit(self._wrapped_download, download_id, download_func, *args, **kwargs)

        with self.lock:
            self.active_downloads[download_id] = {
                'future': future,
                'status': 'starting',
                'args': args,
                'kwargs': kwargs
            }

        return download_id, future

    def _wrapped_download(self, download_id, download_func, *args, **kwargs):
        """Wrapper for download functions with error handling and status tracking."""
        try:
            with self.lock:
                if download_id in self.active_downloads:
                    self.active_downloads[download_id]['status'] = 'running'

            # Execute the download function
            result = download_func(*args, **kwargs)

            with self.lock:
                if download_id in self.active_downloads:
                    self.active_downloads[download_id]['status'] = 'completed'

            return result

        except Exception as e:
            with self.lock:
                if download_id in self.active_downloads:
                    self.active_downloads[download_id]['status'] = 'failed'
                    self.active_downloads[download_id]['error'] = str(e)
            raise e
        finally:
            # Clean up completed downloads after a delay
            threading.Timer(30.0, self._cleanup_download, args=[download_id]).start()

    def _cleanup_download(self, download_id):
        """Clean up completed download from tracking."""
        with self.lock:
            if download_id in self.active_downloads:
                del self.active_downloads[download_id]

    def get_active_downloads(self):
        """Get list of currently active downloads."""
        with self.lock:
            return dict(self.active_downloads)

    def get_download_count(self):
        """Get number of active downloads."""
        with self.lock:
            return len([d for d in self.active_downloads.values() if d['status'] in ['starting', 'running']])

    def shutdown(self):
        """Shutdown the download manager."""
        self.executor.shutdown(wait=True)

# Global download manager instance
download_manager = DownloadManager(max_workers=max_concurrent_downloads)




def get_playlist_songs(url):
    """
    get a list of songs in a spotify playlist from url using requests and beautifulsoup
    """
    r = requests.get(url)
    soup = BeautifulSoup(r.text, "html.parser")
    songs = soup.find_all("div", {"class": "tracklist-col name"})
    song_list = []
    for song in songs:
        sngname = song.find("span").text
        song_list.append(
            {sngname, song.text.replace(sngname, "").split("•")[0].strip()}
        )
    return song_list


class thread(threading.Thread):
    global path

    def __init__(self, cmd, folder=None, autoplay=False, lyrics_toggle=False):
        threading.Thread.__init__(self)
        self.cmd = cmd
        self.folder = folder
        self.autoplay = autoplay
        self.lyrics_toggle = lyrics_toggle

    def mp3(self, task, folder=None):
        ytdl = YoutubeDL(
            {
                "format": "bestaudio",
                "outtmpl": "%(title)s.%(ext)s",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
                "postprocessors": [
                    {
                        "key": "FFmpegExtractAudio",
                        "preferredcodec": "mp3",
                        "preferredquality": "192",
                    }
                ],
            }
        )
        url_filename = ""
        regex_ = regex
        re = r"^.*(youtube|yout)"
        re_1 = r"^.*(open.spotify|spotify.com)"
        if "--folder " not in task and not folder:
            print("no folder specified")
        elif "--folder " in task:
            task, folder = task.split("--folder ")
        if folder:
            info = ""
            # Split variables
            if not os.path.isdir(f"audio\\{folder}"):
                os.makedirs(f"audio\\{folder}")
            if regex_.match(re, task):
                info = ytdl.extract_info(task, download=True)
            elif regex_.match(re_1, task):
                songs = spotify.get_playlist_songs(task)
                for song in songs:
                    info = ytdl.extract_info(
                        f"ytsearch:{song}", download=True)
            else:
                info = ytdl.extract_info(
                    f"ytsearch:{task}", download=True)

            if info:
                if info["title"]:
                    entry = info
                else:
                    entry = info["entries"][0]

                # Get the actual filename that was downloaded
                downloaded_filename = None
                title = entry.get("title", "Unknown")

                # Look for the most recently created mp3 file that matches the title
                mp3_files = [f for f in os.listdir(path) if f.endswith(".mp3")]
                if mp3_files:
                    # Sort by modification time, get the most recent
                    mp3_files.sort(key=lambda x: os.path.getmtime(os.path.join(path, x)), reverse=True)
                    downloaded_filename = mp3_files[0]

                if downloaded_filename:
                    try:
                        source_path = os.path.join(path, downloaded_filename)
                        destination_path = os.path.join(path, "audio", folder, downloaded_filename)

                        # Handle autoplay functionality - copy file directly to clipboard
                        if self.autoplay == 1:
                            print(f"🎵 Adding to Soundpad: {downloaded_filename}")

                            # Call the new Soundpad function to copy file and paste
                            try:
                                # Schedule the Soundpad operation to run in the main thread
                                import threading
                                if hasattr(threading.current_thread(), '_soundpad_callback'):
                                    # Use the actual downloaded file path (before moving to folder)
                                    threading.current_thread()._soundpad_callback(source_path)
                                else:
                                    # Fallback to old method if callback not available
                                    pyautogui.hotkey("ctrl", "shift", "alt", "f7")
                            except Exception as e:
                                print(f"Autoplay error: {e}")
                                # Fallback to old method
                                pyautogui.hotkey("ctrl", "shift", "alt", "f7")

                        # Move the file to the correct folder
                        os.rename(source_path, destination_path)
                        print(f"✅ Moved {downloaded_filename} to audio\\{folder}\\")

                    except FileExistsError:
                        print(f"File {downloaded_filename} already exists in destination")
                        os.remove(source_path)
                    except Exception as e:
                        print(f"Error moving file {downloaded_filename}: {e}")
                else:
                    print("⚠️ No downloaded file found to move")

        else:
            url_filename = task
            if regex_.match(re, task):
                ytdl.download({url_filename})
            else:
                ytdl.download({f"ytsearch:{task}"})


def get_similiratiy(my_list):
    if len(my_list) < 2:
        return 0.0
    x = np.mean(
        [
            SequenceMatcher(None, a, b).ratio()
            for a, b in itertools.combinations(my_list, 2)
        ]
    )
    return x

LIST = [""]

def old_tk():
    # songs = get_list()['items']
    # for song in songs:
    #     artist = song['track']['album']['artists'][0]['name']
    #     song = song['track']['name']
    #     thread("thread-2").mp3(f"{artist} - {song}", folder="smoon")

    def test_c_dl(folder, autoplay=False, lyrics_toggle=False):
        current_media_info = spotifyh.get_current_track()

        # Validate that we actually detected media before starting download
        if not current_media_info or current_media_info.strip() == "":
            print("❌ No currently playing media detected. Cannot download.")
            add_log_message("No currently playing media detected. Please start playing music first.", "error")
            return

        # Check if we're at the download limit
        active_count = download_manager.get_download_count()
        if active_count >= max_concurrent_downloads:
            add_log_message(f"Maximum downloads ({max_concurrent_downloads}) already running. Please wait.", "warning")
            return

        print(f"✅ Detected currently playing: {current_media_info}")
        add_log_message(f"Detected currently playing: {current_media_info}", "info")

        # Submit download to thread pool
        download_id, future = download_manager.submit_download(
            downloadc_threaded, current_media_info, folder, autoplay, lyrics_toggle
        )

        add_log_message(f"Started download {download_id} ({active_count + 1}/{max_concurrent_downloads} active)", "info")

    def downloadc_threaded(current_media_info, folder, autoplay=False, lyrics_toggle=False):
        """Thread-safe download function for use with DownloadManager."""
        # Double-check that we have valid media info (safety check)
        if not current_media_info or current_media_info.strip() == "":
            print("❌ Invalid media info passed to downloadc_threaded. Aborting download.")
            master.after(0, lambda: add_log_message("Download aborted: No valid media information.", "error"))
            return

        # Show download starting message (thread-safe GUI update)
        master.after(0, lambda: add_log_message(f"Starting download: {current_media_info}", "info"))
        time.sleep(0.1)

        print(f"Downloading: {current_media_info}")
        folder_display = folder if folder else "default"
        master.after(0, lambda: add_log_message(f"Target folder: {folder_display}", "info"))

        if folder != "":
            folder_arg = f"--folder {folder}"
        else:
            folder_arg = ""

        x = thread("eek", autoplay=autoplay, lyrics_toggle=lyrics_toggle)

        # Set up Soundpad callback for autoplay (thread-safe)
        if autoplay:
            def soundpad_callback(file_path):
                master.after(100, lambda: activate_soundpad_and_add_file(file_path))
            threading.current_thread()._soundpad_callback = soundpad_callback

        try:
            x.mp3(f"{current_media_info} {folder_arg}")
            x.run()
            folder_clean = folder_arg.replace("--folder ", "")

            # Thread-safe GUI updates
            master.after(0, lambda: update_list(current_media_info))
            master.after(0, lambda: add_log_message(f"Successfully downloaded: {current_media_info}", "success"))

            return True
        except Exception as e:
            print(f"❌ Download failed: {e}")
            master.after(0, lambda: add_log_message(f"Download failed: {current_media_info} - {str(e)}", "error"))
            return False

    def downloadc(current_media_info, folder, autoplay=False, lyrics_toggle=False):
        """Legacy download function - kept for compatibility."""
        return downloadc_threaded(current_media_info, folder, autoplay, lyrics_toggle)

    def download_threaded(dl, folder, autoplay=False, lyricst=False):
        """Thread-safe download function for regular downloads."""
        if not dl or dl.strip() == "":
            master.after(0, lambda: add_log_message("Please enter a song name or URL to download.", "warning"))
            return False

        master.after(0, lambda: add_log_message(f"Starting download: {dl}", "info"))
        folder_display = folder if folder else "default"
        master.after(0, lambda: add_log_message(f"Target folder: {folder_display}", "info"))

        if folder != "":
            folder_arg = f"--folder {folder}"
        else:
            folder_arg = ""

        # Set up Soundpad callback for autoplay
        if autoplay:
            def soundpad_callback(file_path):
                master.after(100, lambda: activate_soundpad_and_add_file(file_path))
            threading.current_thread()._soundpad_callback = soundpad_callback

        try:
            x = thread("eek", autoplay=autoplay, lyrics_toggle=lyricst)
            x.mp3(f"{dl} {folder_arg}")
            x.run()
            master.after(0, lambda: add_log_message(f"Successfully downloaded: {dl}", "success"))
            return True
        except Exception as e:
            master.after(0, lambda: add_log_message(f"Download failed: {dl} - {str(e)}", "error"))
            return False

    def download(dl, folder, autoplay=False, lyricst=False):
        """Main download function with thread management."""
        if not dl or dl.strip() == "":
            add_log_message("Please enter a song name or URL to download.", "warning")
            return

        # Check if we're at the download limit
        active_count = download_manager.get_download_count()
        if active_count >= max_concurrent_downloads:
            add_log_message(f"Maximum downloads ({max_concurrent_downloads}) already running. Please wait.", "warning")
            return

        # Submit download to thread pool
        download_id, future = download_manager.submit_download(
            download_threaded, dl, folder, autoplay, lyricst
        )

        add_log_message(f"Started download {download_id} ({active_count + 1}/{max_concurrent_downloads} active)", "info")

    def opendir():
        global path
        # Get the folder name from the input field
        folder_name = e2.get().strip()

        if folder_name:
            # Open the specific folder if it exists
            folder_path = os.path.join(path, "audio", folder_name)
            if os.path.exists(folder_path):
                print(f"Opening folder: {folder_path}")
                add_log_message(f"Opening folder: {folder_name}", "info")
                subprocess.Popen(f'explorer "{folder_path}"')
            else:
                # Folder doesn't exist, open audio directory instead
                print(f"Folder {folder_name} doesn't exist. Opening audio directory instead.")
                audio_path = os.path.join(path, "audio")
                if not os.path.exists(audio_path):
                    os.makedirs(audio_path)
                subprocess.Popen(f'explorer "{audio_path}"')
                add_log_message(f"Folder '{folder_name}' doesn't exist yet. Opened audio directory.", "warning")
        else:
            # No folder specified, open the main audio directory
            audio_path = os.path.join(path, "audio")
            if not os.path.exists(audio_path):
                os.makedirs(audio_path)
            print(f"Opening audio directory: {audio_path}")
            add_log_message("Opening main audio directory", "info")
            subprocess.Popen(f'explorer "{audio_path}"')

    def listen():
        with sr.Microphone() as source:
            print("Say something!")
            audio = r.listen(source, timeout=3)
        try:
            text = r.recognize_google(audio)
            print("You said: " + text)
            e1.insert(tk.END, f"{text}")
        except sr.UnknownValueError:
            print("Google Speech Recognition could not understand audio")
            return ""
        except sr.RequestError as e:
            print(
                "Could not request results from Google Speech Recognition service; {0}".format(e))
            return ""
        except TimeoutError:
            print("TimeoutError")
            return ""

    def update_list(song):
        # Only add to list if song is valid
        if song and song.strip():
            box_list.append(song)
            listb.insert(tk.END, f"📁 Added to list: {song}\n")
            # Auto-scroll to bottom
            listb.see(tk.END)
        else:
            print("Warning: Attempted to add invalid song to list")

    def add_log_message(message, message_type="info"):
        """Add a formatted log message to the text box."""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")

        # Color coding based on message type
        if message_type == "success":
            prefix = "✅"
        elif message_type == "error":
            prefix = "❌"
        elif message_type == "warning":
            prefix = "⚠️"
        elif message_type == "info":
            prefix = "ℹ️"
        else:
            prefix = "📝"

        formatted_message = f"[{timestamp}] {prefix} {message}\n"
        listb.insert(tk.END, formatted_message)
        listb.see(tk.END)  # Auto-scroll to bottom

    def copy_file_to_clipboard_as_file(file_path):
        """Copy the actual file (not path) to clipboard using PowerShell - no Explorer windows."""
        try:
            # Method 1: Use PowerShell FileDropList (best for Soundpad)
            add_log_message("Using PowerShell FileDropList to copy file...", "info")

            # PowerShell script to copy file as FileDropList
            powershell_script = f'''
Add-Type -AssemblyName System.Windows.Forms
$file = Get-Item "{file_path}"
$fileList = New-Object System.Collections.Specialized.StringCollection
$fileList.Add($file.FullName)
[System.Windows.Forms.Clipboard]::SetFileDropList($fileList)
Write-Host "File copied to clipboard as FileDropList"
'''

            result = subprocess.run(['powershell', '-Command', powershell_script],
                                  capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                add_log_message("File copied to clipboard using FileDropList", "success")
                return True
            else:
                add_log_message(f"FileDropList method failed: {result.stderr}", "warning")

        except Exception as e:
            add_log_message(f"FileDropList method failed: {e}", "warning")

        # Method 2: Use PowerShell with temporary script file (more reliable)
        try:
            add_log_message("Using PowerShell script file method...", "info")

            import tempfile

            # Create a temporary script to copy file to clipboard
            temp_script = tempfile.NamedTemporaryFile(mode='w', suffix='.ps1', delete=False)
            temp_script.write(f'''
Add-Type -AssemblyName System.Windows.Forms
$file = Get-Item "{file_path}"
$fileList = New-Object System.Collections.Specialized.StringCollection
$fileList.Add($file.FullName)
[System.Windows.Forms.Clipboard]::SetFileDropList($fileList)
Write-Host "File successfully copied to clipboard"
''')
            temp_script.close()

            # Execute the PowerShell script
            result = subprocess.run(['powershell', '-ExecutionPolicy', 'Bypass', '-File', temp_script.name],
                                  capture_output=True, text=True, timeout=10)

            # Clean up temp script
            os.unlink(temp_script.name)

            if result.returncode == 0:
                add_log_message("File copied using PowerShell script", "success")
                return True
            else:
                add_log_message(f"PowerShell script failed: {result.stderr}", "warning")

        except Exception as e:
            add_log_message(f"PowerShell script method failed: {e}", "warning")

        # Method 3: Simple PowerShell Set-Clipboard (fallback)
        try:
            add_log_message("Using simple PowerShell Set-Clipboard...", "info")

            # Simple PowerShell command
            powershell_cmd = f'Get-Item "{file_path}" | Set-Clipboard'
            result = subprocess.run(['powershell', '-Command', powershell_cmd],
                                  capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                add_log_message("File copied using simple PowerShell", "success")
                return True
            else:
                add_log_message(f"Simple PowerShell failed: {result.stderr}", "warning")

        except Exception as e:
            add_log_message(f"Simple PowerShell method failed: {e}", "error")

        return False

    def add_file_to_soundpad(file_path):
        """Add file to Soundpad by copying file to clipboard and pasting."""
        try:
            add_log_message("Copying file to clipboard for Soundpad...", "info")

            # Method 1: Use PowerShell FileDropList (most reliable for Soundpad)
            if copy_file_to_clipboard_as_file(file_path):
                add_log_message("File copied to clipboard, pasting into Soundpad...", "info")

                # Small delay to ensure clipboard is ready
                time.sleep(0.2)

                # Paste the file directly into Soundpad
                pyautogui.hotkey('ctrl', 'v')
                time.sleep(0.5)  # Give Soundpad time to process

                add_log_message("File pasted into Soundpad", "success")
                return True

        except Exception as e:
            add_log_message(f"Primary file copy method failed: {e}", "warning")

        # Method 2: Direct PowerShell approach without Explorer
        try:
            add_log_message("Trying direct PowerShell file copy...", "info")

            # Use PowerShell to copy file directly to clipboard as FileDropList
            powershell_script = f'''
Add-Type -AssemblyName System.Windows.Forms
$file = Get-Item "{file_path}"
$fileList = New-Object System.Collections.Specialized.StringCollection
$fileList.Add($file.FullName)
[System.Windows.Forms.Clipboard]::SetFileDropList($fileList)
Write-Host "File added to clipboard"
'''

            result = subprocess.run(['powershell', '-Command', powershell_script],
                                  capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                add_log_message("File copied using direct PowerShell", "info")

                # Paste into Soundpad
                time.sleep(0.2)
                pyautogui.hotkey('ctrl', 'v')
                time.sleep(0.5)

                add_log_message("File pasted into Soundpad via PowerShell", "success")
                return True
            else:
                add_log_message(f"PowerShell method failed: {result.stderr}", "warning")

        except Exception as e:
            add_log_message(f"PowerShell method failed: {e}", "warning")

        # Method 3: Use Soundpad's file import hotkey (if user has it configured)
        try:
            add_log_message("Trying Soundpad import hotkey...", "info")

            # Copy file path to clipboard for file dialog
            win32clipboard.OpenClipboard()
            win32clipboard.EmptyClipboard()
            win32clipboard.SetClipboardText(file_path)
            win32clipboard.CloseClipboard()

            # Try common Soundpad import hotkeys
            # Many users configure Ctrl+Shift+A or Ctrl+O for "Add sound"
            pyautogui.hotkey('ctrl', 'shift', 'a')
            time.sleep(0.5)

            # If file dialog opens, paste path and confirm
            pyautogui.hotkey('ctrl', 'v')
            time.sleep(0.2)
            pyautogui.press('enter')

            add_log_message("Used Soundpad import hotkey method", "info")
            return True

        except Exception as e:
            add_log_message(f"Import hotkey method failed: {e}", "warning")

        add_log_message("All Soundpad integration methods failed", "error")
        return False

    def find_soundpad_window():
        """Find Soundpad window handle."""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                if "Soundpad" in window_text:
                    windows.append(hwnd)
            return True

        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        return windows[0] if windows else None

    def activate_soundpad_and_add_file(file_path):
        """
        Activate Soundpad, add the file, and return to the previous window.
        Improved to prevent Soundpad from closing and avoid opening Explorer.
        """
        try:
            # Get the currently active window
            current_window = win32gui.GetForegroundWindow()
            current_window_title = win32gui.GetWindowText(current_window)

            add_log_message(f"Current active window: {current_window_title}", "info")

            # Find Soundpad window
            soundpad_hwnd = find_soundpad_window()
            if not soundpad_hwnd:
                # Try to launch Soundpad if not found
                add_log_message("Soundpad not found, attempting to launch...", "warning")
                try:
                    subprocess.Popen("soundpad.exe")
                    time.sleep(3)  # Wait for Soundpad to start
                    soundpad_hwnd = find_soundpad_window()
                except Exception as e:
                    add_log_message(f"Failed to launch Soundpad: {e}", "error")
                    return False

            if not soundpad_hwnd:
                add_log_message("Could not find or launch Soundpad", "error")
                return False

            # Gently activate Soundpad without forcing it
            try:
                # Restore window if minimized (but don't force focus yet)
                win32gui.ShowWindow(soundpad_hwnd, win32con.SW_RESTORE)
                time.sleep(0.3)

                # Gently bring Soundpad to foreground
                try:
                    win32gui.SetForegroundWindow(soundpad_hwnd)
                    add_log_message("Soundpad gently activated", "info")
                except:
                    # If that fails, just make sure it's visible
                    win32gui.ShowWindow(soundpad_hwnd, win32con.SW_SHOW)
                    add_log_message("Soundpad made visible", "info")

                time.sleep(0.5)  # Give Soundpad time to become ready

            except Exception as e:
                add_log_message(f"Soundpad activation warning: {e}", "warning")
                # Continue anyway

            # Add the file to Soundpad using the improved method
            add_log_message("Adding file to Soundpad...", "info")
            if add_file_to_soundpad(file_path):
                add_log_message("File successfully added to Soundpad", "success")
            else:
                add_log_message("Failed to add file to Soundpad", "error")
                return False

            # Return to the previous window more gently
            if current_window != soundpad_hwnd and win32gui.IsWindow(current_window):
                time.sleep(1.0)  # Give Soundpad time to process the file
                try:
                    # Gently return to previous window
                    win32gui.ShowWindow(current_window, win32con.SW_RESTORE)
                    time.sleep(0.2)
                    win32gui.SetForegroundWindow(current_window)

                    add_log_message(f"Returned to: {current_window_title}", "info")
                except Exception as e:
                    add_log_message(f"Could not return to previous window: {e}", "warning")

            return True

        except Exception as e:
            add_log_message(f"Error in Soundpad autoplay: {e}", "error")
            return False

    def clear_log():
        """Clear the log text box and show welcome message."""
        listb.delete("0.0", tk.END)
        show_welcome_message()

    def show_welcome_message():
        """Show the welcome message in the text box."""
        listb.insert("0.0", "🎵 YTDL Music Downloader Ready!\n")
        listb.insert(tk.END, "=" * 60 + "\n")
        listb.insert(tk.END, "📋 Quick Guide:\n")
        listb.insert(tk.END, "  • Enter a YouTube URL or search term above\n")
        listb.insert(tk.END, "  • Optionally specify a folder name\n")
        listb.insert(tk.END, "  • Click 'Download Current Song' to download what's playing\n")
        listb.insert(tk.END, "  • Use 'Voice Input' for hands-free operation\n")
        listb.insert(tk.END, "  • All download progress will appear here\n")
        listb.insert(tk.END, f"  • Maximum concurrent downloads: {max_concurrent_downloads}\n")
        listb.insert(tk.END, "=" * 60 + "\n\n")

    def show_download_status():
        """Show current download status."""
        active_downloads = download_manager.get_active_downloads()
        active_count = len([d for d in active_downloads.values() if d['status'] in ['starting', 'running']])

        if active_count == 0:
            add_log_message("No active downloads", "info")
        else:
            add_log_message(f"Active downloads: {active_count}/{max_concurrent_downloads}", "info")
            for download_id, info in active_downloads.items():
                if info['status'] in ['starting', 'running']:
                    status_emoji = "🔄" if info['status'] == 'running' else "⏳"
                    add_log_message(f"  {status_emoji} {download_id}: {info['status']}", "info")

    def cancel_all_downloads():
        """Cancel all active downloads."""
        global download_manager
        try:
            download_manager.shutdown()
            download_manager = DownloadManager(max_workers=max_concurrent_downloads)
            add_log_message("All downloads cancelled and manager restarted", "warning")
        except Exception as e:
            add_log_message(f"Error cancelling downloads: {e}", "error")

    master = tk.CTk()
    master.geometry("800x600")  # Larger window for better layout
    master.title("YTDL Music Downloader")
    master.minsize(600, 400)  # Set minimum window size

    # Configure main window grid weights for responsive layout
    master.grid_columnconfigure(0, weight=1)
    master.grid_rowconfigure(1, weight=1)  # Make the text area expandable

    # Top frame for controls
    frame_1 = tk.CTkFrame(master, corner_radius=10)
    frame_1.grid(row=0, column=0, sticky="ew", padx=20, pady=(20, 10))

    # Configure frame_1 grid weights
    frame_1.grid_columnconfigure(1, weight=1)  # Make entry fields expandable

    box_list = []

    # Get current date in format YYYY-MM-DD
    date = datetime.datetime.now().strftime("%Y-%m-%d")
    list_of_folders = [f"Date {date}", "DnB", "Koshka", "Rap", "Memes", "Other"]

    # Input fields with better styling
    tk.CTkLabel(frame_1, text="Song/Playlist URL:", font=tk.CTkFont(size=12, weight="bold")).grid(
        row=0, column=0, sticky="w", padx=(15, 10), pady=(15, 5))
    e1 = tk.CTkEntry(frame_1, placeholder_text="Enter YouTube URL or search term...", height=32)
    e1.grid(row=0, column=1, sticky="ew", padx=(0, 15), pady=(15, 5))

    tk.CTkLabel(frame_1, text="Folder Name:", font=tk.CTkFont(size=12, weight="bold")).grid(
        row=1, column=0, sticky="w", padx=(15, 10), pady=5)
    e2 = tk.CTkEntry(frame_1, placeholder_text="Enter folder name (optional)...", height=32)
    e2.grid(row=1, column=1, sticky="ew", padx=(0, 15), pady=5)

    # Button frame for better organization
    button_frame = tk.CTkFrame(frame_1, fg_color="transparent")
    button_frame.grid(row=2, column=0, columnspan=2, sticky="ew", padx=15, pady=(10, 15))

    # Configure button frame grid
    button_frame.grid_columnconfigure((0, 1, 2), weight=1)

    # Main action buttons
    tk.CTkButton(
        button_frame, text="Download",
        command=lambda: download(e1.get(), e2.get(), autoplay_toggle.get(), lyrics_toggle.get()),
        height=36, font=tk.CTkFont(size=12, weight="bold")
    ).grid(row=0, column=0, sticky="ew", padx=(0, 5), pady=5)

    tk.CTkButton(
        button_frame, text="Download Current Song",
        command=lambda: test_c_dl(e2.get(), autoplay=autoplay_toggle.get(), lyrics_toggle=lyrics_toggle.get()),
        height=36, font=tk.CTkFont(size=12, weight="bold")
    ).grid(row=0, column=1, sticky="ew", padx=5, pady=5)

    tk.CTkButton(
        button_frame, text="Open Folder", command=opendir,
        height=36, font=tk.CTkFont(size=12, weight="bold")
    ).grid(row=0, column=2, sticky="ew", padx=(5, 0), pady=5)

    # Utility buttons
    utility_frame = tk.CTkFrame(button_frame, fg_color="transparent")
    utility_frame.grid(row=1, column=0, columnspan=3, sticky="ew", pady=(5, 0))
    utility_frame.grid_columnconfigure((0, 1, 2, 3, 4, 5), weight=1)

    tk.CTkButton(utility_frame, text="Clear URL", command=lambda: e1.delete(0, tk.END), height=28).grid(
        row=0, column=0, sticky="ew", padx=(0, 1))
    tk.CTkButton(utility_frame, text="Voice Input", command=lambda: listen(), height=28).grid(
        row=0, column=1, sticky="ew", padx=1)
    tk.CTkButton(utility_frame, text="Clear Log", command=lambda: clear_log(), height=28).grid(
        row=0, column=2, sticky="ew", padx=1)
    tk.CTkButton(utility_frame, text="Download Status", command=lambda: show_download_status(), height=28).grid(
        row=0, column=3, sticky="ew", padx=1)
    tk.CTkButton(utility_frame, text="Cancel All", command=lambda: cancel_all_downloads(), height=28).grid(
        row=0, column=4, sticky="ew", padx=(1, 0))

    # Initialize variables
    autoplay_toggle = tk.IntVar()
    autoplay_toggle.set(1)
    lyrics_toggle = tk.IntVar()
    lyrics_toggle.set(1)

    # Options frame
    options_frame = tk.CTkFrame(utility_frame, fg_color="transparent")
    options_frame.grid(row=0, column=5, sticky="ew", padx=(1, 0))

    tk.CTkCheckBox(options_frame, text="Auto Play", variable=autoplay_toggle,
                   font=tk.CTkFont(size=11)).pack(side="left", padx=5)

    # Bottom frame for the text box (log/output area)
    frame_2 = tk.CTkFrame(master, corner_radius=10)
    frame_2.grid(row=1, column=0, sticky="nsew", padx=20, pady=(0, 20))

    # Configure frame_2 grid weights for responsive text box
    frame_2.grid_columnconfigure(0, weight=1)
    frame_2.grid_rowconfigure(0, weight=1)

    # Enhanced text box with better styling
    listb = tk.CTkTextbox(
        frame_2,
        font=tk.CTkFont(family="Consolas", size=11),  # Monospace font for better readability
        wrap="word",
        corner_radius=8,
        border_width=2
    )
    listb.grid(row=0, column=0, sticky="nsew", padx=15, pady=15)

    # Add welcome message to the text box
    show_welcome_message()

    def on_closing():
        """Handle application closing - cleanup downloads."""
        try:
            download_manager.shutdown()
            print("Download manager shut down successfully")
        except Exception as e:
            print(f"Error shutting down download manager: {e}")
        finally:
            master.destroy()

    master.protocol("WM_DELETE_WINDOW", on_closing)
    master.mainloop()



if __name__ == "__main__":
    
    tk.set_appearance_mode("dark")
    tk.set_default_color_theme("dark-blue")

    old_tk()
    # app = tk.CTk()
    # app.geometry("450x300")

    # # add 2 input boxes, one for the name of song and the folder to save the file to

    # # add a button that calls the download function
    
    # input_frame = tk.CTkFrame(app)
    # input_frame.pack(pady=20, padx=60, fill="both", expand=True)

    # song_label = tk.CTkLabel(input_frame, text="Enter a song or playlist url")
    # song_label.pack()

    # song_input = tk.CTkEntry(input_frame)
    # song_input.pack()

    # folder_label = tk.CTkLabel(input_frame, text="Enter a folder name")
    # folder_label.pack()

    # folder_input = tk.CTkEntry(input_frame)
    # folder_input.pack()


    # download_button = tk.CTkButton(input_frame, text="Download", command=lambda: download(song_input.get(), folder_input.get()))
    # download_button.pack()

    # button_frame = tk.CTkFrame(app)

    # button_frame.pack(pady=20, padx=60, fill="both", expand=True)


    # # add a button that calls the downloadc function

    # download_current_button = tk.CTkButton(button_frame, text="Download Currently Playing song", command=lambda: downloadc(folder_input.get()))
    # download_current_button.pack()

    # app.title("YTDL CLI")
    # app.mainloop()
