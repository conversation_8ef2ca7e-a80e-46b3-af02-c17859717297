import threading
import os
import regex
import spotify
from colorama import Fore, init
import psutil
import time
import subprocess
import requests
from bs4 import BeautifulSoup
from yt_dlp import YoutubeDL
from difflib import SequenceMatcher
import numpy as np
import itertools
import customtkinter as tk
import datetime
# import random
import win32gui
# import win32con
import spotifyh
from spotifyh import get_list
import speech_recognition as sr
import shutil
import pyautogui
import argparse


r = sr.Recognizer()


if psutil.Process(os.getpid()).parent().name() == "cmd.exe":
    init(convert=True)

path = f"{os.getcwd()}\\"
sList = []




def get_playlist_songs(url):
    """
    get a list of songs in a spotify playlist from url using requests and beautifulsoup
    """
    r = requests.get(url)
    soup = BeautifulSoup(r.text, "html.parser")
    songs = soup.find_all("div", {"class": "tracklist-col name"})
    song_list = []
    for song in songs:
        sngname = song.find("span").text
        song_list.append(
            {sngname, song.text.replace(sngname, "").split("•")[0].strip()}
        )
    return song_list


class thread(threading.Thread):
    global path

    def __init__(self, cmd, folder=None, autoplay=False, lyrics_toggle=False):
        threading.Thread.__init__(self)
        self.cmd = cmd
        self.folder = folder
        self.autoplay = autoplay
        self.lyrics_toggle = lyrics_toggle

    def mp3(self, task, folder=None):
        ytdl = YoutubeDL(
            {
                "format": "bestaudio",
                "outtmpl": "%(title)s.%(ext)s",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
                "postprocessors": [
                    {
                        "key": "FFmpegExtractAudio",
                        "preferredcodec": "mp3",
                        "preferredquality": "192",
                    }
                ],
            }
        )
        url_filename = ""
        regex_ = regex
        re = r"^.*(youtube|yout)"
        re_1 = r"^.*(open.spotify|spotify.com)"
        if "--folder " not in task and not folder:
            print("no folder specified")
        elif "--folder " in task:
            task, folder = task.split("--folder ")
        if folder:
            info = ""
            # Split variables
            if not os.path.isdir(f"audio\\{folder}"):
                os.makedirs(f"audio\\{folder}")
            if regex_.match(re, task):
                info = ytdl.extract_info(task, download=True)
            elif regex_.match(re_1, task):
                songs = spotify.get_playlist_songs(task)
                for song in songs:
                    info = ytdl.extract_info(
                        f"ytsearch:{song}", download=True)
            else:
                info = ytdl.extract_info(
                    f"ytsearch:{task}", download=True)

            if info:
                if info["title"]:
                    entry = info
                else:
                    entry = info["entries"][0]
                prepared_file_name = ytdl.prepare_filename(entry)
                fn = ".".join(prepared_file_name.split(".")[:-1]).strip()
                # Get the latest file in folder

                for file in os.listdir(path):
                    if file.endswith(".mp3"):
                        try:
                            if self.autoplay == 1:
                                extension = file.split(".")[-1]
                                if os.path.exists(f"misc\\autoplay.{extension}"):
                                    os.remove(f"misc\\autoplay.{extension}")
                                shutil.copy(
                                    f"{path}{file}",
                                    f"misc\\autoplay.{extension}",
                                )
                                pyautogui.hotkey("ctrl", "shift", "alt", "f7")
                            os.rename(
                                f"{path}\\{file}", f"{path}\\audio\\{folder}\\{file}"
                            )
                        except FileExistsError:
                            print("file already exists")
                            os.remove(f"{path}\\{file}")

        else:
            url_filename = task
            if regex_.match(re, task):
                ytdl.download({url_filename})
            else:
                ytdl.download({f"ytsearch:{task}"})


def get_similiratiy(my_list):
    if len(my_list) < 2:
        return 0.0
    x = np.mean(
        [
            SequenceMatcher(None, a, b).ratio()
            for a, b in itertools.combinations(my_list, 2)
        ]
    )
    return x

LIST = [""]

def old_tk():
    # songs = get_list()['items']
    # for song in songs:
    #     artist = song['track']['album']['artists'][0]['name']
    #     song = song['track']['name']
    #     thread("thread-2").mp3(f"{artist} - {song}", folder="smoon")

    def test_c_dl(folder, autoplay=False, lyrics_toggle=False):
        current_media_info = spotifyh.get_current_track()

        # Validate that we actually detected media before starting download
        if not current_media_info or current_media_info.strip() == "":
            print("❌ No currently playing media detected. Cannot download.")
            # Show error message to user in the GUI
            listb.insert(tk.END, "❌ No currently playing media detected. Please start playing music first.\n")
            return

        print(f"✅ Detected currently playing: {current_media_info}")
        new_thread = threading.Thread(target=downloadc, args=(current_media_info, folder, autoplay, lyrics_toggle))
        new_thread.start()

    def downloadc(current_media_info, folder, autoplay=False, lyrics_toggle=False):
        # Double-check that we have valid media info (safety check)
        if not current_media_info or current_media_info.strip() == "":
            print("❌ Invalid media info passed to downloadc. Aborting download.")
            listb.insert(tk.END, "❌ Download aborted: No valid media information.\n")
            return

        # Show download starting message
        listb.insert(tk.END, f"🎵 Starting download: {current_media_info}...\n")
        time.sleep(0.1)

        print(f"Downloading: {current_media_info}")
        if folder != "":
            folder = f"--folder {folder}"
        else:
            folder = ""
        x = thread("eek", autoplay=autoplay, lyrics_toggle=lyrics_toggle)

        try:
            x.mp3(f"{current_media_info} {folder}")
            x.run()
            folder = folder.replace("--folder ", "")
            update_list(current_media_info)
            listb.insert(tk.END, f"✅ Successfully downloaded: {current_media_info}\n")
        except Exception as e:
            print(f"❌ Download failed: {e}")
            listb.insert(tk.END, f"❌ Download failed: {current_media_info} - {str(e)}\n")

    def download(dl, folder, autoplay=False, lyricst=False):
        if folder != "":
            folder = f"--folder {folder}"
        else:
            folder = ""
        x = thread("eek", autoplay=autoplay, lyrics_toggle=lyricst)
        x.mp3(f"{dl} {folder}")
        x.run()
        #text_box.insert(tk.END, f"{dl} saved to {folder}\n")

    def opendir():
        global path
        print(path)
        subprocess.Popen(f'explorer "{path}"')

    def listen():
        with sr.Microphone() as source:
            print("Say something!")
            audio = r.listen(source, timeout=3)
        try:
            text = r.recognize_google(audio)
            print("You said: " + text)
            e1.insert(tk.END, f"{text}")
        except sr.UnknownValueError:
            print("Google Speech Recognition could not understand audio")
            return ""
        except sr.RequestError as e:
            print(
                "Could not request results from Google Speech Recognition service; {0}".format(e))
            return ""
        except TimeoutError:
            print("TimeoutError")
            return ""

    def update_list(song):
        # Only add to list if song is valid
        if song and song.strip():
            box_list.append(song)
            listb.insert(tk.END, f"📁 Added to list: {song}\n")
        else:
            print("Warning: Attempted to add invalid song to list")

    master = tk.CTk()
    master.geometry("550x350")

    master.title = "Spotify Downloader"

    frame_1 = tk.CTkFrame(master)
    box_list = []

    # Get current date in format YYYY-MM-DD
    date = datetime.datetime.now().strftime("%Y-%m-%d")
    list_of_folders = [f"Date {date}", "DnB",
                       "Koshka", "Rap", "Memes", "Other"]

    tk.CTkLabel(frame_1, text="Enter a song or playlist url", width=20).grid(row=0)
    tk.CTkLabel(frame_1, text="Enter a folder name", width=20).grid(row=1)
    e1 = tk.CTkEntry(frame_1)
    e1.grid(row=0, column=1)
    e2 = tk.CTkEntry(frame_1)
    e2.grid(row=1, column=1)

    autoplay_toggle = tk.IntVar()
    tk.CTkButton(frame_1, text="Refresh", command=lambda: update_list()).grid(
        row=4, column=2)

    # e1 clear button
    tk.CTkButton(frame_1, text="Clear", command=lambda: e1.delete(0, tk.END)).grid(
        row=4, column=0
    )
    # todo: fix this
    # tk.Button(frame_1, text="Clear", command=e1.delete(first=1.0)).grid(row=4, column=0)

    tk.CTkButton(frame_1, text="Listen", command=lambda: listen()).grid(
        row=4, column=2, sticky=tk.W
    )

    autoplay_toggle.set(1)
    

    tk.CTkButton(
        frame_1, text="Download", command=lambda: download(e1.get(), e2.get(), autoplay_toggle.get(), lyrics_toggle.get())
    ).grid(row=3, column=0, padx=10, sticky=tk.W)
    tk.CTkButton(
        frame_1,
        text="Download Currently Playing song",
        command=lambda: test_c_dl(
            e2.get(), autoplay=autoplay_toggle.get(), lyrics_toggle=lyrics_toggle.get()),
    ).grid(row=3, column=1, sticky=tk.W)
    tk.CTkButton(frame_1, text="Open directory", command=opendir).grid(
        row=3, column=2, sticky=tk.W
    )
    # new text box that spans entire window

    # text_box = tk.Text(frame_2, height=20, width=50)
    # text_box
    # change text_box font
    # text_box.config(font=("ubumtu", 11))
    frame_1.pack()

    ## frame 2

    frame_2 = tk.CTkFrame(master)
    # create a text box that will show the list of songs that are downloaded
    listb = tk.CTkTextbox(frame_2, height=150, width=350)
    listb.grid(row=0, column=0)

    soption = tk.CTkCheckBox(frame_2, text="Auto Play",
                             variable=autoplay_toggle).grid(row=3, column=0, sticky=tk.E, columnspan=2, padx=10)
    lyrics_toggle = tk.IntVar()
    # soption1 = tk.CTkCheckBox(frame_2, text="Music mode", variable=lyrics_toggle).grid(
    #     row=4, column=0, sticky=tk.E, columnspan=2, padx=10)
    
    lyrics_toggle.set(1)
    
    frame_2.pack(side=tk.LEFT, padx=25, pady=10)

    master.mainloop()        



if __name__ == "__main__":
    
    tk.set_appearance_mode("dark")
    tk.set_default_color_theme("dark-blue")

    old_tk()
    # app = tk.CTk()
    # app.geometry("450x300")

    # # add 2 input boxes, one for the name of song and the folder to save the file to

    # # add a button that calls the download function
    
    # input_frame = tk.CTkFrame(app)
    # input_frame.pack(pady=20, padx=60, fill="both", expand=True)

    # song_label = tk.CTkLabel(input_frame, text="Enter a song or playlist url")
    # song_label.pack()

    # song_input = tk.CTkEntry(input_frame)
    # song_input.pack()

    # folder_label = tk.CTkLabel(input_frame, text="Enter a folder name")
    # folder_label.pack()

    # folder_input = tk.CTkEntry(input_frame)
    # folder_input.pack()


    # download_button = tk.CTkButton(input_frame, text="Download", command=lambda: download(song_input.get(), folder_input.get()))
    # download_button.pack()

    # button_frame = tk.CTkFrame(app)

    # button_frame.pack(pady=20, padx=60, fill="both", expand=True)


    # # add a button that calls the downloadc function

    # download_current_button = tk.CTkButton(button_frame, text="Download Currently Playing song", command=lambda: downloadc(folder_input.get()))
    # download_current_button.pack()

    # app.title("YTDL CLI")
    # app.mainloop()
