import win32gui
from threading import Thread as thread
import time
import requests
import win32process
# import wmi  # Temporarily disabled due to hanging issues
import asyncio
from winsdk.windows.media.control import GlobalSystemMediaTransportControlsSessionManager as MediaManager

# c = wmi.WMI()  # Temporarily disabled


def get_app_path(hwnd):
    """Get application path given hwnd. Simplified version without WMI."""
    try:
        # For now, we'll use a simpler approach based on window titles
        # This is less precise but avoids WMI hanging issues
        title = win32gui.GetWindowText(hwnd)
        if "Spotify" in title:
            return "Spotify.exe"  # Simulate Spotify path
        return None
    except Exception as e:
        return None

def get_spotify_hwnd():
    # get all running apps
    apps = []
    win32gui.EnumWindows(lambda hwnd, lst: lst.append((hwnd, win32gui.GetWindowText(hwnd))), apps)

    for app in apps:
        exe = get_app_path(app[0])
        title, hwnd = app[1], app[0]
        if exe and 'Spotify' in exe:
            return hwnd

    return None

async def get_media_info():
    """Get currently playing media information using Windows Media Session API."""
    try:
        sessions = await MediaManager.request_async()

        # Try current session first
        current_session = sessions.get_current_session()
        if current_session:
            info = await current_session.try_get_media_properties_async()
            if info and (info.title or info.artist):
                artist = info.artist if info.artist else ""
                title = info.title if info.title else ""

                # Format the output similar to how Spotify window titles work
                if artist and title:
                    return f"{artist} - {title}"
                elif title:
                    return title

        # If no current session or no info, try all sessions
        all_sessions = sessions.get_sessions()
        for session in all_sessions:
            try:
                info = await session.try_get_media_properties_async()
                if info and (info.title or info.artist):
                    artist = info.artist if info.artist else ""
                    title = info.title if info.title else ""

                    if artist and title:
                        return f"{artist} - {title}"
                    elif title:
                        return title
            except Exception:
                continue  # Skip sessions that error out

        return None
    except Exception as e:
        print(f"Error getting media info: {e}")
        return None

def get_current_track_media_session():
    """Get current track using Windows Media Session API (synchronous wrapper)."""
    try:
        # Check if there's already an event loop running
        try:
            loop = asyncio.get_running_loop()
            # If we're already in an event loop, we can't use run_until_complete
            print("Warning: Already in an event loop, cannot get media info synchronously")
            return None
        except RuntimeError:
            # No event loop running, we can create one
            pass

        # Run the async function with a timeout
        async def get_media_with_timeout():
            return await asyncio.wait_for(get_media_info(), timeout=5.0)

        # Run the async function in a new event loop
        try:
            result = asyncio.run(get_media_with_timeout())
            return result
        except asyncio.TimeoutError:
            print("Timeout getting media info")
            return None
    except Exception as e:
        print(f"Error in get_current_track_media_session: {e}")
        return None

def get_list():
    base_url = 'https://api.spotify.com/v1/'
    data = {
    'grant_type': 'client_credentials',
    'client_id': "f82f2f7e248b4476b8c8c1c99ebe52da",
    'client_secret': "74c17aa8fb5246ec936f40365c810315",
    }
    auth_response = requests.post("https://accounts.spotify.com/api/token", data=data)
    access_token = auth_response.json().get('access_token')
    headers = {
        'Authorization': 'Bearer {}'.format(access_token),
        'limit': "200",
        'offset': "100"
    }
    playlist_id = "0Bg2iB1sWam4TyYVZEsC7U"
    r = requests.get(f'https://api.spotify.com/v1/playlists/{playlist_id}/tracks?offset=100&limit=100', headers=headers)
    return r.json()
data = get_list()
pass
def get_media_from_browser():
    """Get currently playing media from browser windows (YouTube, etc.)."""
    try:
        apps = []
        win32gui.EnumWindows(lambda hwnd, lst: lst.append((hwnd, win32gui.GetWindowText(hwnd))), apps)

        for app in apps:
            title, hwnd = app[1], app[0]
            if title:
                # Check for YouTube in browser
                if "YouTube" in title and (" - " in title):
                    # Extract song info from YouTube title
                    # Format is usually "Song Title - Artist - YouTube"
                    parts = title.split(" - ")
                    if len(parts) >= 2:
                        # Remove "YouTube" from the end if present
                        if parts[-1].strip() == "YouTube":
                            parts = parts[:-1]
                        if len(parts) >= 2:
                            return f"{parts[1]} - {parts[0]}"  # Artist - Title format
                        else:
                            return parts[0]  # Just the title

                # Check for other music streaming services
                music_services = ["Spotify", "Apple Music", "Deezer", "Tidal", "SoundCloud"]
                for service in music_services:
                    if service in title and " - " in title:
                        # Remove service name from title
                        clean_title = title.replace(f" - {service}", "").strip()
                        if clean_title:
                            return clean_title

        return None
    except Exception as e:
        print(f"Error getting media from browser: {e}")
        return None

def get_current_track():
    """Get current track using multiple detection methods."""

    # Method 1: Try Windows Media Session API (with timeout protection)
    # Temporarily disabled due to hanging issues
    # try:
    #     media_info = get_current_track_media_session()
    #     if media_info:
    #         return media_info
    # except Exception as e:
    #     pass  # Silently fail and try other methods

    # Method 2: Try Spotify window title
    try:
        hwnd = get_spotify_hwnd()
        if hwnd:
            window_title = win32gui.GetWindowText(hwnd)
            if window_title:
                # Clean up Spotify window title (remove " - Spotify" suffix if present)
                if window_title.endswith(" - Spotify"):
                    clean_title = window_title[:-9]  # Remove " - Spotify"
                else:
                    clean_title = window_title

                if clean_title.strip() and clean_title != "Spotify":  # Make sure it's not empty or just "Spotify"
                    return clean_title
    except Exception:
        pass  # Silently fail and try other methods

    # Method 3: Try browser windows (YouTube, etc.)
    try:
        browser_media = get_media_from_browser()
        if browser_media:
            return browser_media
    except Exception:
        pass  # Silently fail

    # No media detected
    return None



if __name__ == "__main__":
    get_spotify_hwnd()