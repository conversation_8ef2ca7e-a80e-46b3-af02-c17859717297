import win32gui
from threading import Thread as thread
import time
import requests
import win32process
import wmi

c = wmi.WMI()


def get_app_path(hwnd):
    """Get applicatin path given hwnd."""
    try:
        
        _, pid = win32process.GetWindowThreadProcessId(hwnd)
        for p in c.query('SELECT ExecutablePath FROM Win32_Process WHERE ProcessId = %s' % str(pid)):
            exe = p.ExecutablePath
            break
    except Exception as e:
        return None
    else:
        return exe
    
def get_spotify_hwnd():
    # get all running apps
    apps = []
    win32gui.EnumWindows(lambda hwnd, lst: lst.append((hwnd, win32gui.GetWindowText(hwnd))), apps)

    for app in apps:
        exe = get_app_path(app[0])
        title, hwnd = app[1], app[0]
        if exe and 'Spotify' in exe:
            return hwnd

    return None

def get_list():
    base_url = 'https://api.spotify.com/v1/'
    data = {
    'grant_type': 'client_credentials',
    'client_id': "f82f2f7e248b4476b8c8c1c99ebe52da",
    'client_secret': "74c17aa8fb5246ec936f40365c810315",
    }
    auth_response = requests.post("https://accounts.spotify.com/api/token", data=data)
    access_token = auth_response.json().get('access_token')
    headers = {
        'Authorization': 'Bearer {}'.format(access_token),
        'limit': "200",
        'offset': "100"
    }
    playlist_id = "0Bg2iB1sWam4TyYVZEsC7U"
    r = requests.get(f'https://api.spotify.com/v1/playlists/{playlist_id}/tracks?offset=100&limit=100', headers=headers)
    return r.json()
data = get_list()
pass
def get_current_track():
    hwnd = get_spotify_hwnd()
    if hwnd:
        return win32gui.GetWindowText(hwnd)



if __name__ == "__main__":
    get_spotify_hwnd()