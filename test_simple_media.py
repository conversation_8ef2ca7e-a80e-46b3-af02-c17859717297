#!/usr/bin/env python3
"""
Simple test for Windows Media Session API.
"""

import asyncio
from winsdk.windows.media.control import GlobalSystemMediaTransportControlsSessionManager as MediaManager

async def test_media_simple():
    """Simple test of the media session API."""
    try:
        print("Requesting media session manager...")
        sessions = await MediaManager.request_async()
        print("Got session manager")

        # Get all sessions
        all_sessions = sessions.get_sessions()
        print(f"Found {len(all_sessions)} total sessions")

        # Try current session first
        current_session = sessions.get_current_session()
        print(f"Current session: {current_session}")

        if current_session:
            print("Getting media properties from current session...")
            info = await current_session.try_get_media_properties_async()
            print(f"Media info: {info}")

            if info:
                print(f"Artist: {info.artist}")
                print(f"Title: {info.title}")
                print(f"Album: {info.album_title}")
                return

        # If no current session, try all sessions
        print("No current session, checking all sessions...")
        for i, session in enumerate(all_sessions):
            print(f"Checking session {i+1}...")
            try:
                info = await session.try_get_media_properties_async()
                if info and (info.title or info.artist):
                    print(f"Found media in session {i+1}:")
                    print(f"  Artist: {info.artist}")
                    print(f"  Title: {info.title}")
                    print(f"  Album: {info.album_title}")
                    return
            except Exception as e:
                print(f"  Error getting info from session {i+1}: {e}")

        print("No media found in any session")

    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("Testing Windows Media Session API directly...")
    asyncio.run(test_media_simple())

if __name__ == "__main__":
    main()
