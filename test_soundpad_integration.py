#!/usr/bin/env python3
"""
Test script for the improved Soundpad integration.
"""

import os
import sys
import subprocess
import tempfile
import time

# Add the current directory to the path so we can import modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_file():
    """Create a test audio file."""
    test_file = "test_soundpad.mp3"
    with open(test_file, 'wb') as f:
        # Write some fake MP3 header bytes
        f.write(b'ID3\x03\x00\x00\x00\x00\x00\x00')
        f.write(b'Test audio content for Soundpad integration' * 50)
    return os.path.abspath(test_file)

def test_powershell_filedroplist(file_path):
    """Test PowerShell FileDropList method."""
    print(f"Testing PowerShell FileDropList for: {file_path}")
    print("-" * 50)
    
    try:
        # Test the exact PowerShell script used in the application
        powershell_script = f'''
Add-Type -AssemblyName System.Windows.Forms
$file = Get-Item "{file_path}"
$fileList = New-Object System.Collections.Specialized.StringCollection
$fileList.Add($file.FullName)
[System.Windows.Forms.Clipboard]::SetFileDropList($fileList)
Write-Host "File copied to clipboard as FileDropList"
'''
        
        result = subprocess.run(['powershell', '-Command', powershell_script], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ PowerShell FileDropList method succeeded")
            print(f"Output: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ PowerShell FileDropList failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ PowerShell FileDropList test failed: {e}")
        return False

def test_clipboard_verification():
    """Test if file is properly in clipboard."""
    print("\nTesting clipboard verification...")
    print("-" * 50)
    
    try:
        # Check clipboard contents
        check_script = '''
Add-Type -AssemblyName System.Windows.Forms
$clipboard = [System.Windows.Forms.Clipboard]::GetDataObject()
if ($clipboard.GetDataPresent([System.Windows.Forms.DataFormats]::FileDrop)) {
    $files = $clipboard.GetData([System.Windows.Forms.DataFormats]::FileDrop)
    Write-Host "Files in clipboard: $($files.Count)"
    foreach ($file in $files) {
        Write-Host "  - $file"
    }
    Write-Host "SUCCESS: Files found in clipboard"
} else {
    Write-Host "No files in clipboard"
}
'''
        
        result = subprocess.run(['powershell', '-Command', check_script], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ Clipboard check succeeded")
            print(f"Result: {result.stdout.strip()}")
            return "SUCCESS: Files found in clipboard" in result.stdout
        else:
            print(f"❌ Clipboard check failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Clipboard verification failed: {e}")
        return False

def test_soundpad_detection():
    """Test Soundpad window detection."""
    print("\nTesting Soundpad detection...")
    print("-" * 50)
    
    try:
        import win32gui
        
        def find_soundpad():
            def enum_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    title = win32gui.GetWindowText(hwnd)
                    if "Soundpad" in title:
                        windows.append((hwnd, title))
                return True
            
            windows = []
            win32gui.EnumWindows(enum_callback, windows)
            return windows[0] if windows else None
        
        soundpad_info = find_soundpad()
        if soundpad_info:
            hwnd, title = soundpad_info
            print(f"✅ Found Soundpad: {title}")
            
            # Test window state
            import win32con
            placement = win32gui.GetWindowPlacement(hwnd)
            state = placement[1]
            
            if state == win32con.SW_SHOWMINIMIZED:
                print("ℹ️ Soundpad is minimized")
            elif state == win32con.SW_SHOWMAXIMIZED:
                print("ℹ️ Soundpad is maximized")
            else:
                print("ℹ️ Soundpad is in normal state")
            
            return True
        else:
            print("⚠️ Soundpad not running")
            print("   Please start Soundpad to test integration")
            return False
            
    except Exception as e:
        print(f"❌ Soundpad detection failed: {e}")
        return False

def test_implementation_improvements():
    """Test implementation improvements in main.py."""
    print("\nTesting implementation improvements...")
    print("-" * 50)
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        improvements = {
            "No Explorer opening": "explorer /select" not in content or content.count("explorer /select") <= 1,
            "FileDropList method": "SetFileDropList" in content,
            "Gentle window activation": "gently" in content.lower(),
            "No forced focus": "SetFocus" not in content or content.count("SetFocus") <= 1,
            "Proper timing": "time.sleep" in content,
            "Error handling": "except Exception" in content,
            "Multiple PowerShell methods": content.count("powershell") >= 2,
            "Clipboard verification": "GetDataObject" in content or "clipboard" in content.lower(),
        }
        
        all_good = True
        for improvement, present in improvements.items():
            status = "✅" if present else "❌"
            print(f"{status} {improvement}")
            if not present:
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ Implementation test failed: {e}")
        return False

def cleanup():
    """Clean up test files."""
    try:
        if os.path.exists("test_soundpad.mp3"):
            os.remove("test_soundpad.mp3")
        print("✅ Cleaned up test files")
    except Exception as e:
        print(f"⚠️ Cleanup warning: {e}")

def main():
    print("Soundpad Integration Fix Test")
    print("=" * 60)
    
    test_results = []
    test_file = None
    
    try:
        # Create test file
        test_file = create_test_file()
        print(f"Created test file: {test_file}")
        
        # Run tests
        test_results.append(("Implementation Improvements", test_implementation_improvements()))
        test_results.append(("Soundpad Detection", test_soundpad_detection()))
        test_results.append(("PowerShell FileDropList", test_powershell_filedroplist(test_file)))
        test_results.append(("Clipboard Verification", test_clipboard_verification()))
        
        # Summary
        print("\n" + "=" * 60)
        print("Test Results Summary:")
        print("-" * 25)
        
        passed = 0
        for test_name, result in test_results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} {test_name}")
            if result:
                passed += 1
        
        print(f"\nPassed: {passed}/{len(test_results)} tests")
        
        if passed >= 3:  # At least 3 out of 4 tests should pass
            print("\n🎉 Soundpad integration improvements are working!")
            print("\nFixed Issues:")
            print("• No more Explorer windows opening")
            print("• Soundpad won't close unexpectedly")
            print("• Proper file copying to clipboard")
            print("• Gentle window activation")
            print("• Multiple reliable methods")
        else:
            print("\n⚠️ Some tests failed. Please check the implementation.")
            
        if not test_results[1][1]:  # Soundpad detection failed
            print("\n💡 Tip: Start Soundpad to test the full integration")
    
    finally:
        cleanup()

if __name__ == "__main__":
    main()
