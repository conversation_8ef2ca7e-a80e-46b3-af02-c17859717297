#!/usr/bin/env python3
"""
Simple test for window title detection without WMI.
"""

import win32gui

def get_all_window_titles():
    """Get all window titles."""
    apps = []
    win32gui.EnumWindows(lambda hwnd, lst: lst.append((hwnd, win32gui.GetWindowText(hwnd))), apps)
    
    print("All window titles:")
    for i, (hwnd, title) in enumerate(apps):
        if title.strip():  # Only show non-empty titles
            print(f"{i+1:3d}: {title}")
    
    return apps

def find_media_windows(apps):
    """Find windows that might contain media information."""
    media_windows = []
    
    for hwnd, title in apps:
        if title:
            # Check for various media indicators
            if any(keyword in title.lower() for keyword in ['spotify', 'youtube', 'music', 'soundcloud', 'deezer', 'tidal']):
                media_windows.append((hwnd, title))
    
    return media_windows

def main():
    print("Testing window title detection...")
    print("=" * 50)
    
    try:
        apps = get_all_window_titles()
        print(f"\nFound {len(apps)} total windows")
        
        print("\n" + "=" * 50)
        print("Looking for media-related windows...")
        
        media_windows = find_media_windows(apps)
        if media_windows:
            print(f"Found {len(media_windows)} media-related windows:")
            for hwnd, title in media_windows:
                print(f"  📀 {title}")
        else:
            print("No media-related windows found")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
